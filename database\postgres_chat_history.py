"""
PostgreSQL-based chat message history implementation for LangChain.
"""

import logging
import uuid
from typing import List, Optional, Sequence
import psycopg
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import BaseMessage, message_to_dict, messages_from_dict
from .connection import get_sync_connection
from .models import create_tables, DEFAULT_TABLE_NAME

logger = logging.getLogger(__name__)

class PostgresChatMessageHistory(BaseChatMessageHistory):
    """
    PostgreSQL-based chat message history implementation.
    
    This class stores chat messages in a PostgreSQL database table and provides
    LangChain-compatible interface for message history management.
    """
    
    def __init__(
        self,
        session_id: str,
        table_name: str = DEFAULT_TABLE_NAME,
        connection: Optional[psycopg.Connection] = None,
        auto_create_table: bool = True
    ):
        """
        Initialize PostgresChatMessageHistory.
        
        Args:
            session_id: Unique identifier for the chat session.
            table_name: Name of the PostgreSQL table to store messages.
            connection: Optional database connection. If None, creates new connections as needed.
            auto_create_table: Whether to automatically create the table if it doesn't exist.
        """
        self.session_id = session_id
        self.table_name = table_name
        self._connection = connection
        
        if auto_create_table:
            self._ensure_table_exists()
    
    def _ensure_table_exists(self) -> None:
        """Ensure the message history table exists."""
        try:
            if self._connection:
                create_tables(self._connection, self.table_name)
            else:
                create_tables(table_name=self.table_name)
        except Exception as e:
            logger.exception(f"Failed to ensure table {self.table_name} exists: {e}")
            raise
    
    def _get_connection(self) -> psycopg.Connection:
        """Get a database connection."""
        if self._connection:
            return self._connection
        return get_sync_connection()
    
    @property
    def messages(self) -> List[BaseMessage]:
        """Retrieve all messages for the current session."""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        f"""
                        SELECT type, content, additional_kwargs 
                        FROM {self.table_name} 
                        WHERE session_id = %s 
                        ORDER BY created_at ASC
                        """,
                        (self.session_id,)
                    )
                    
                    rows = cursor.fetchall()
                    
                    # Convert rows to message dictionaries
                    message_dicts = []
                    for row in rows:
                        message_dict = {
                            "type": row["type"],
                            "data": {
                                "content": row["content"],
                                "additional_kwargs": row["additional_kwargs"] or {}
                            }
                        }
                        message_dicts.append(message_dict)
                    
                    # Convert to LangChain messages
                    return messages_from_dict(message_dicts)
                    
        except Exception as e:
            logger.exception(f"Failed to retrieve messages for session {self.session_id}: {e}")
            return []
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a single message to the history."""
        self.add_messages([message])
    
    def add_messages(self, messages: Sequence[BaseMessage]) -> None:
        """Add multiple messages to the history."""
        if not messages:
            return
            
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # Prepare batch insert
                    insert_sql = f"""
                        INSERT INTO {self.table_name} 
                        (session_id, message_id, type, content, additional_kwargs)
                        VALUES (%s, %s, %s, %s, %s)
                    """
                    
                    # Convert messages to database format
                    values = []
                    for message in messages:
                        message_dict = message_to_dict(message)
                        message_id = str(uuid.uuid4())
                        
                        values.append((
                            self.session_id,
                            message_id,
                            message_dict["type"],
                            message_dict["data"]["content"],
                            message_dict["data"].get("additional_kwargs", {})
                        ))
                    
                    # Execute batch insert
                    cursor.executemany(insert_sql, values)
                    conn.commit()
                    
                    logger.debug(f"Added {len(messages)} messages to session {self.session_id}")
                    
        except Exception as e:
            logger.exception(f"Failed to add messages to session {self.session_id}: {e}")
            raise
    
    def clear(self) -> None:
        """Clear all messages for the current session."""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        f"DELETE FROM {self.table_name} WHERE session_id = %s",
                        (self.session_id,)
                    )
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
                    logger.info(f"Cleared {deleted_count} messages for session {self.session_id}")
                    
        except Exception as e:
            logger.exception(f"Failed to clear messages for session {self.session_id}: {e}")
            raise
    
    def get_message_count(self) -> int:
        """Get the number of messages in the current session."""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        f"SELECT COUNT(*) as count FROM {self.table_name} WHERE session_id = %s",
                        (self.session_id,)
                    )
                    result = cursor.fetchone()
                    return result["count"] if result else 0
                    
        except Exception as e:
            logger.exception(f"Failed to get message count for session {self.session_id}: {e}")
            return 0
    
    @classmethod
    def create_tables(cls, connection: psycopg.Connection, table_name: str = DEFAULT_TABLE_NAME) -> None:
        """
        Create the message history table.
        
        Args:
            connection: Database connection.
            table_name: Name of the table to create.
        """
        create_tables(connection, table_name)
